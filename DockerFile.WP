# Stage 1: Build the project using Node.js and npm
FROM node:14-bullseye AS builder

WORKDIR /app

# Copy only package files to leverage Docker cache
COPY themes/sellerlogic-theme/package*.json ./

# Install system dependencies first
RUN apt-get update && \
    apt-get install -y nasm libpng-dev libjpeg-dev build-essential python2 python3 make g++ python3-distutils && \
    rm -rf /var/lib/apt/lists/*

# Create python symlink for node-gyp compatibility
RUN ln -sf /usr/bin/python2 /usr/bin/python

# Install dependencies with specific node-sass configuration
RUN npm config set unsafe-perm true && \
    npm config set python /usr/bin/python2 && \
    npm install --no-optional --legacy-peer-deps

# Install mozjpeg separately
RUN npm install mozjpeg --no-optional

# Copy the rest of the source code
COPY themes/sellerlogic-theme /app
COPY languages/loco /app/languages/loco

# Build the project
RUN npm run build-deploy

# Stage 2: Prepare the final image with Word<PERSON>ress and Nginx
FROM bitnami/wordpress-nginx:6.6.2

ENV PHP_DEFAULT_OPCACHE_MEMORY_CONSUMPTION=512
ENV PHP_DEFAULT_OPCACHE_INTERNED_STRINGS_BUFFER=64
ENV PHP_DEFAULT_OPCACHE_MAX_ACCELERATED_FILES=40000
ENV PHP_DEFAULT_OPCACHE_REVALIDATE_FREQ=0
ENV PHP_DEFAULT_OPCACHE_VALIDATE_TIMESTAMPS=0
ENV PHP_DEFAULT_MEMORY_LIMIT=512M

USER root

COPY docker/rootfs /

# Install necessary packages
RUN apt-get update && apt-get install -y unzip wget autoconf build-essential && \
    rm -rf /var/lib/apt/lists/*

# Clean and prepare directories
RUN rm -rf /opt/bitnami/wordpress/wp-content/plugins && \
    mkdir -p /opt/bitnami/wordpress/wp-content/plugins && \
    rm -rf /opt/bitnami/wordpress/wp-content/languages && \
    mkdir -p /opt/bitnami/wordpress/wp-content/languages

# Copy other necessary files
COPY themes/sellerlogic-theme /opt/bitnami/wordpress/wp-content/themes/sellerlogic-theme
COPY plugins /opt/bitnami/wordpress/wp-content/plugins
COPY languages /opt/bitnami/wordpress/wp-content/languages
COPY config/WPML-home-redirect.php config/constant.php config/config-env-emails.php /opt/bitnami/wordpress/
COPY config/php/php.ini /opt/bitnami/php/etc/php.ini
COPY config/php/www.conf /opt/bitnami/php/etc/php-fpm.d/www.conf

# Copy built files from the builder stage
COPY --from=builder /app/languages/loco /opt/bitnami/wordpress/wp-content/languages/loco
COPY --from=builder /app/theme_plugins/gutenberg/_dist/prod /opt/bitnami/wordpress/wp-content/themes/sellerlogic-theme/theme_plugins/gutenberg/_dist/prod
COPY --from=builder /app/theme_plugins/data-layer/_dist/js /opt/bitnami/wordpress/wp-content/themes/sellerlogic-theme/theme_plugins/data-layer/_dist/js
COPY --from=builder /app/_dist/prod /opt/bitnami/wordpress/wp-content/themes/sellerlogic-theme/_dist/prod
COPY --from=builder /app/theme_settings/auto_generate_entrypoints.php /opt/bitnami/wordpress/wp-content/themes/sellerlogic-theme/theme_settings/auto_generate_entrypoints.php

# Set permissions
RUN mkdir -p "/bitnami/wordpress-nginx" && \
    chown -R 1001:root "/bitnami/wordpress-nginx" && \
    chmod -R g+rwX "/bitnami/wordpress-nginx" && \
    sed -i '/mkdir -p "\/bitnami\/wordpress-nginx"/d' /post-init.sh && \
    chmod g+rwX /opt/bitnami && \
    /opt/bitnami/scripts/mysql-client/postunpack.sh && \
    /opt/bitnami/scripts/nginx/postunpack.sh && \
    sed -i 's|^export PHP_DEFAULT_OPCACHE_INTERNED_STRINGS_BUFFER=.*|export PHP_DEFAULT_OPCACHE_INTERNED_STRINGS_BUFFER="${PHP_DEFAULT_OPCACHE_INTERNED_STRINGS_BUFFER:-16}"|' /opt/bitnami/scripts/php-env.sh && \
    /opt/bitnami/scripts/php/postunpack.sh && \
    /opt/bitnami/scripts/nginx-php-fpm/postunpack.sh && \
    /opt/bitnami/scripts/wordpress/postunpack.sh && \
    sed -i '/define( '\''WP_DEBUG'\'', false );/d' /opt/bitnami/wordpress/wp-config.php && \
    chown -R 1001:0 /bitnami

# Install redis object cache
RUN wget https://github.com/nicolasff/phpredis/archive/master.zip \
    && unzip master.zip \
    && cd phpredis-develop \
    && phpize \
    && ./configure \
    && make \
    && make install \
    && cd .. \
    && rm -rf phpredis-develop \
    && rm master.zip \
    && composer require predis/predis

USER 1001
