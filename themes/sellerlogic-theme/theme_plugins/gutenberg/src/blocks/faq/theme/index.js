import './index.scss';

document.addEventListener('DOMContentLoaded', function(){
	const faqBlocks = document.querySelectorAll('.theme-block-faq .wrap-list-questions');
	faqBlocks.forEach((block, index) => {
		if(index !== 0){
			block.style.display = 'none';
		}
	});

	const spoilerContents = document.querySelectorAll('.theme-block-faq .wrap-list-questions .spoiler-content');
	spoilerContents.forEach(spoiler => {
		spoiler.style.display = 'none';
	});

	const activeTab = function(element){
		const catID = element.getAttribute('data-catid');
		const parent = element.closest('.wrap-inner-faq');
		const questionsBlocks = parent.querySelectorAll('.wrap-list-questions');

		Array.from(element.parentElement.children).forEach(sibling => {
			sibling.classList.remove('active');
		});

		element.classList.add('active');

		questionsBlocks.forEach(block => {
			block.style.display = 'none';
		});

		questionsBlocks.forEach(block => {
			if(block.id.includes(catID)){
				block.style.display = '';
			}
		});
	};

	const firstTab = document.querySelector('.theme-block-faq .wrap-faq-cat-list [data-catid]');
	if(firstTab){
		activeTab(firstTab);
	}

	document.addEventListener('click', function(e){
		const target = e.target.closest('.theme-block-faq .wrap-faq-cat-list [data-catid]');
		if(target){
			activeTab(target);
		}
	});

	document.addEventListener('click', function(e){
		const target = e.target.closest('.theme-block-faq .wrap-list-questions .wrap-title');
		if(target){
			const siblingContent = target.nextElementSibling;
			if(siblingContent && siblingContent.classList.contains('spoiler-content')){
				if(siblingContent.style.display === 'none' || !siblingContent.style.display){
					siblingContent.style.display = 'block';
					const height = siblingContent.scrollHeight + 'px';
					siblingContent.style.height = height;
					siblingContent.classList.add('expanded');
				}else{
					siblingContent.classList.remove('expanded');
					siblingContent.style.height = 0;
					siblingContent.addEventListener('transitionend', function handleTransitionEnd(){
						siblingContent.style.display = 'none';
						siblingContent.removeEventListener('transitionend', handleTransitionEnd);
					});
				}
			}
		}
	});

	const hash = location.hash;
	if(hash && !hash.match(/^#\d+/)){
		const currentQuestion = document.querySelector(`.theme-block-faq .theme-block-faq-spoiler ${hash}`);
		if(currentQuestion){
			const currentFaqCatEl = currentQuestion.closest('[id^="categoryID"]');
			const currentCatID = currentFaqCatEl?.id;
			const currentFaqEl = currentFaqCatEl?.closest('.theme-block-faq');
			const currentCatListEl = currentFaqEl?.querySelector(`[data-catid="${currentCatID}"]`);
			if(currentCatListEl){
				activeTab(currentCatListEl);
			}
			currentQuestion.scrollIntoView({behavior: 'smooth', block: 'center'});
		}
	}
});
