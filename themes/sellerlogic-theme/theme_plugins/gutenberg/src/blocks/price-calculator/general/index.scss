@use '../../../../../../_src/scss/_mixins/media/breakpoints' as brs;
@use '../../../../../../_src/scss/_mixins/form/switch' as *;
@use '../nouislider/nouislider';
@use './cards';
@use './table';

.theme-block-price-calculator{
	margin-right: auto;
	margin-left: auto;

	.pricing-title{
		font-size: 36px;
		line-height: 1.39;
		text-align: center;
		margin: 40px 0;

		@include brs.mobile(){
			margin: 24px 0;
		}
		@include brs.small_mobile(){
			font-size: 24px;
			line-height: 1.4;
			margin: 20px 0;
		}
	}

	&__header{
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		padding-bottom: 40px;
		gap: 14px;

		@include brs.tablet(){
			flex-direction: column;
			gap: 14px;

			.theme-block-price-calculator__billing{
				justify-content: flex-start;
			}
		}
	}

	&__title{
		font-size: 28px;
		font-weight: 300;
		line-height: 1.5;

		@include brs.small_mobile(){
			font-size: 18px;
			line-height: 1.44;
		}
	}

	&__text{
		margin-right: 5px;
	}

	&__amount{
		font-size: 36px;
		font-weight: bold;

		@include brs.small_mobile(){
			display: block;
			font-size: 24px;
			line-height: 1.4;
		}
	}

	&__range{
		padding-bottom: 36px;

		@include brs.small_mobile(){
			padding-bottom: 10px;
		}
	}

	&__billing{
		display: flex;
		align-items: center;
		justify-content: flex-end;
		gap: 10px;

		@include brs.mobile(){
			flex-wrap: wrap;
		}
	}

	&__billing-text{
		font-size: 18px;
		line-height: 1.44;

		@include brs.small_mobile(){
			font-size: 16px;
			line-height: 1.5;
		}
	}

	&__free-months{
		display: flex;
		font-size: 12px;
		font-weight: bold;
		text-align: center;
		color: white;
		align-items: center;
		justify-content: center;
		flex-direction: row;
		padding: 9px 20px;
		border: none;
		border-radius: 2000px;
		background-color: #FFA500;
	}

	&__switch{
		@include switch();
	}

	.noUi-pips{

		.noUi-value{
			transform: translateX(-50%);

			@include brs.small_mobile(){
				display: none;
			}

			&:nth-child(1 of .noUi-value){
				transform: none;
			}

			&:nth-last-child(1 of .noUi-value){
				transform: translateX(-100%);
			}
		}

		.noUi-marker{
			@include brs.from-small_mobile(){
				// Hack to fix the penultimate marker, to move it a bit to the right.
				&:nth-last-child(2 of .noUi-marker){
					margin-left: 0.5%;
				}
			}
		}
	}
}