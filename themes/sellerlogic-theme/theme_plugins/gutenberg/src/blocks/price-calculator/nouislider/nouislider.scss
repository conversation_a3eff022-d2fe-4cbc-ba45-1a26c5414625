@use '../../../../../../_src/scss/_mixins/media/breakpoints' as brs;

.noUi-target{
	margin-top: 30px;
	border: 0;
	border-radius: 2000px;
	background: #d5dce0;
	box-shadow: none;

	@include brs.small_mobile(){
		padding-right: 10px;
	}
}

.noUi-base{

}

.noUi-connects{
	border-radius: 2000px;
	cursor: pointer;
}

.noUi-connect{
	background: #0055cc;
}

.noUi-horizontal{
	.noUi-origin{
		@include brs.small_mobile(){
			width: calc(100% - 10px) !important;
		}
	}

	.noUi-handle{
		top: -4px;
		right: -12px;
		width: 24px;
		height: 24px;
		border: 0;
		border-radius: 50%;
		cursor: grab;
		background-color: #0055cc;
		box-shadow: 0 1px 8px 0 rgba(0, 0, 0, 0.3);

		&:active{
			cursor: grabbing;
		}

		&:before{
			top: 4px;
			left: 4px;
			width: 16px;
			height: 16px;
			border-radius: 50%;
			background-color: white;
			box-shadow: none;
		}

		&:after{
			display: none;
		}
	}
}

.noUi-pips-horizontal{
	top: 0;
	height: 90px;
	margin-top: -30px;
	padding: 0;
}

.noUi-touch-area{

}

.noUi-pips{

	.noUi-marker{
		height: 5px;
		margin-top: 5px;
		background: #d5dce0;

		&:nth-child(odd of .noUi-marker){
			height: 10px;
			margin: 0;
		}
	}

	.noUi-value{
		bottom: 0;
		font-size: 16px;
		line-height: 1.5;
		color: #000;
		height: 24px;
	}
}