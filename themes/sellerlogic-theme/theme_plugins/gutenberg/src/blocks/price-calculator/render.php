<?php

use ThemeBlock\ThemeRenderBlock;

const SUBSCRIPTION_TYPE_TRIAL = 'TRIAL';
const SUBSCRIPTION_TYPE_FREEMIUM = 'FREEMIUM';
const SUBSCRIPTION_TYPE_STARTER = 'STARTER';
const SUBSCRIPTION_TYPE_ADVANCED = 'ADVANCED';

const KB_ARTICLE_LINKS = [
	'All Amazon marketplaces' => [
		'en' => 'https://support.sellerlogic.com/en_US/amazon-marketplaces',
		'de' => 'https://support.sellerlogic.com/de_DE/amazon-marketplaces',
		'fr' => 'https://support.sellerlogic.com/fr_FR/amazon-marketplaces',
		'es' => 'https://support.sellerlogic.com/es_ES/amazon-marketplaces',
	],
	'Event scheduler' => [
		'en' => 'https://support.sellerlogic.com/en_US/products/event-scheduler',
		'de' => 'https://support.sellerlogic.com/de_DE/products/event-scheduler',
		'fr' => 'https://support.sellerlogic.com/fr_FR/producte/event-scheduler',
		'es' => 'https://support.sellerlogic.com/es_ES/products/event-scheduler',
	],
	'Multi currency' => [
		'en' => 'https://support.sellerlogic.com/en_US/profile_currency',
		'de' => 'https://support.sellerlogic.com/de_DE/profile_currency',
		'fr' => 'https://support.sellerlogic.com/fr_FR/profile_currency',
		'es' => 'https://support.sellerlogic.com/es_ES/profile_currency',
	],
	'B2C AI Repricing & Rule-based' => [
		'en' => 'https://support.sellerlogic.com/en_US/repricer/optimization-strategies',
		'de' => 'https://support.sellerlogic.com/de_DE/repricer/optimization-strategies',
		'fr' => 'https://support.sellerlogic.com/fr_FR/repricer/optimization-strategies',
		'es' => 'https://support.sellerlogic.com/es_ES/repricer/optimization-strategies',
	],
	'B2B AI Repricing & Rule-based' => [
		'en' => 'https://support.sellerlogic.com/en_US/repricer/optimization-strategies',
		'de' => 'https://support.sellerlogic.com/de_DE/repricer/optimization-strategies',
		'fr' => 'https://support.sellerlogic.com/fr_FR/repricer/optimization-strategies',
		'es' => 'https://support.sellerlogic.com/es_ES/repricer/optimization-strategies',
	],
	'Automatic Min & Max' => [
		'en' => 'https://support.sellerlogic.com/en_US/min-price-max-price',
		'de' => 'https://support.sellerlogic.com/de_DE/min-price-max-price',
		'fr' => 'https://support.sellerlogic.com/fr_FR/min-price-max-price',
		'es' => 'https://support.sellerlogic.com/es_ES/min-price-max-price',
	],
	'Product & Stock synchronization from Amazon' => [
		'en' => 'https://support.sellerlogic.com/faqs/how-often-does-repricer-synchronize-products-and-stock-with-amazon',
		'de' => 'https://support.sellerlogic.com/de_DE/faqs/how-often-does-repricer-synchronize-products-and-stock-with-amazon',
		'fr' => 'https://support.sellerlogic.com/fr_FR/faqs/how-often-does-repricer-synchronize-products-and-stock-with-amazon',
		'es' => 'https://support.sellerlogic.com/es_ES/faqs/how-often-does-repricer-synchronize-products-and-stock-with-amazon',
	],
	'Bulk editing of settings' => [
		'en' => 'https://support.sellerlogic.com/en_US/my-products_bulk-edit',
		'de' => 'https://support.sellerlogic.com/de_DE/my-products_bulk-edit',
		'fr' => 'https://support.sellerlogic.com/fr_FR/my-products_bulk-edit',
		'es' => 'https://support.sellerlogic.com/es_ES/my-products_bulk-edit',
	],
	'Import operations' => [
		'en' => 'https://support.sellerlogic.com/en_US/repricer_import-product-settings',
		'de' => 'https://support.sellerlogic.com/de_DE/repricer_import-product-settings',
		'fr' => 'https://support.sellerlogic.com/fr_FR/repricer_import-product-settings',
		'es' => 'https://support.sellerlogic.com/es_ES/repricer_import-product-settings',
	],
	'Export operations' => [
		'en' => 'https://support.sellerlogic.com/en_US/import-export/repricer_export-product-settings',
		'de' => 'https://support.sellerlogic.com/de_DE/import-export/repricer_export-product-settings',
		'fr' => 'https://support.sellerlogic.com/fr_FR/repricer-import-export/repricer_export-product-settings',
		'es' => 'https://support.sellerlogic.com/es_ES/import-export/repricer_export-product-settings',
	],
	'Cost synchronization with Business Analytics' => [
		'en' => 'https://support.sellerlogic.com/en_US/manage-costs#synchronize-with-repricer-9',
		'de' => 'https://support.sellerlogic.com/de_DE/manage-costs#mit-repricer-synchronisieren-9',
		'fr' => 'https://support.sellerlogic.com/fr_FR/manage-costs#synchroniser-avec-repricer-9',
		'es' => 'https://support.sellerlogic.com/es_ES/manage-costs#sincronizar-con-repricer-9',
	],
	'Dedicated Onboarding Specialist' => [
		'en' => 'https://support.sellerlogic.com/en_US/support-menu',
		'de' => 'https://support.sellerlogic.com/de_DE/support-menu',
		'fr' => 'https://support.sellerlogic.com/fr_FR/support-menu',
		'es' => 'https://support.sellerlogic.com/es_ES/support-menu',
	],
	'API' => [
		'en' => 'https://support.sellerlogic.com/en_US/505867-settings-menu/api-settings',
		'de' => 'https://support.sellerlogic.com/de_DE/505867-settings-menu/api-settings',
		'fr' => 'https://support.sellerlogic.com/fr_FR/einstellungsmen%C3%BC/api-settings',
		'es' => 'https://support.sellerlogic.com/es_ES/505867-settings-menu/api-settings',
	],
	'User permissions' => [
		'en' => 'https://support.sellerlogic.com/en_US/manage-user-profiles-and-roles',
		'de' => 'https://support.sellerlogic.com/de_DE/manage-user-profiles-and-roles',
		'fr' => 'https://support.sellerlogic.com/fr_FR/manage-user-profiles-and-roles',
		'es' => 'https://support.sellerlogic.com/es_ES/manage-user-profiles-and-roles',
	],
];

ThemeGutenberg\Responsive\Responsive::register_responsiveness(
	'theme/price-calculator',
	[]
);

new ThemeRenderBlock('theme/price-calculator', function(ThemeRenderBlock $class){
	$id = $class->get_gb_identifier();
	$tooltip_product_optimization_text = __('The price per Product Optimization is an estimated daily cost for the entire subscription, intended for informational and comparison purposes only. Please be aware that manual calculations might yield different results as costs may vary depending on the number of days in a month and the actual usage of Product Optimizations. This does not affect the subscription price, which is the same from month to month.', TRS_DOMAIN);
	?>

	<div <?php $class->id_attr(); ?> class="<?php $class->classes(); ?>">
		<h2 class="pricing-title"><?php _e('Find the perfect Repricer plan for your needs', TRS_DOMAIN); ?></h2>
		<div class="theme-block-price-calculator__header">
			<div class="theme-block-price-calculator__title">
				<span class="theme-block-price-calculator__text"><?php _e('Average of daily optimized products', TRS_DOMAIN); ?>:</span>
				<span class="theme-block-price-calculator__amount">0</span>
			</div>
			<div class="theme-block-price-calculator__billing">
				<span class="theme-block-price-calculator__billing-text">
					<span data-pricing-text-type="annual"><?php _e('Annually billing', TRS_DOMAIN); ?>:</span>
					<span data-pricing-text-type="monthly" style="display: none"><?php _e('Monthly billing', TRS_DOMAIN); ?>:</span>
				</span>
				<span class="theme-block-price-calculator__free-months" data-pricing-hide-when-type="monthly"><?php printf(_n('Get %d month free', 'Get %d months free', 2, TRS_DOMAIN), 2); ?></span>
				<label class="theme-block-price-calculator__switch" for="one_year_checkbox<?php echo $id; ?>">
					<input id="one_year_checkbox<?php echo $id; ?>" name="one_year_checkbox" type="checkbox" checked>
					<span class="theme-block-price-calculator__switch--slider"></span>
				</label>
			</div>
		</div>
		<div class="theme-block-price-calculator__range">
			<div class="js-price-range"></div>
		</div>
		<div class="theme-block-price-calculator__cards">
			<div class="pricing">
				<div data-subscription-type="<?php echo SUBSCRIPTION_TYPE_TRIAL; ?>" class="pricing__plan pricing__plan--trial">
					<div class="pricing__plan-header">
						<div class="pricing__plan-header--content">
							<div class="pricing__plan-header--inner-content">
								<h2 class="pricing__title"><?php _e('Trial', TRS_DOMAIN); ?></h2>
								<h3 class="pricing__price"><?php printf(_n('%d day', '%d days', 14, TRS_DOMAIN), 14); ?></h3>
							</div>
						</div>
						<div class="pricing__plan-header--footer">
							<div class="pricing__plan-header--footer-inner">
								<ul class="pricing__info">
									<li class="pricing__info-item">
										<div class="pricing__ingo_item-wrap_price">
											<strong data-price-type="optimization">0.00€</strong>
											<span class="pricing__info--value"> / <?php _e('Product Optimization', TRS_DOMAIN); ?></span>
										</div>
										<span class="icon-circle-info" data-tooltip-content="<?php echo $tooltip_product_optimization_text; ?>"></span>
									</li>
									<li class="pricing__info-item">
										<div class="pricing__ingo_item-wrap_price">
											<strong data-price-type="on-demand-optimization">0.00€</strong>
											<span class="pricing__info--value"> / <?php _e('On-Demand Product Optimization', TRS_DOMAIN); ?></span>
										</div>
									</li>
								</ul>
								<a href="<?php SettingsPage::value('client_sellerlogic_register'); ?>" class="cta-button pricing__button RP-upper-trial-button"><?php _e('Get started', TRS_DOMAIN); ?></a>
							</div>
						</div>
					</div>
					<ul class="pricing__features">
						<li class="pricing__feature"><?php _e('All Amazon marketplaces', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('Event scheduler', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('Multi currency', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('B2C AI Repricing & Rule-based', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('B2B AI Repricing & Rule-based', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('Automatic Min & Max', TRS_DOMAIN); ?></li>
						<li class="pricing__feature">
							<?php _e('Product & Stock synchronization from Amazon', TRS_DOMAIN); ?>:
							<strong><?php printf(_n('every %d hour', 'every %d hours', 2, TRS_DOMAIN), 2); ?></strong>
						</li>
						<li class="pricing__feature"><?php _e('Bulk editing of settings', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('Import operations', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('Export operations', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('Dedicated Onboarding Specialist', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('API', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('User permissions', TRS_DOMAIN); ?></li>
					</ul>
				</div>
				<div data-subscription-type="<?php echo SUBSCRIPTION_TYPE_FREEMIUM; ?>" class="pricing__plan pricing__plan--freemium">
					<div class="pricing__plan-header">
						<div class="pricing__plan-header--content">
							<div class="pricing__plan-header--inner-content">
								<h2 class="pricing__title"><?php _e('Freemium', TRS_DOMAIN); ?></h2>
								<h3 class="pricing__price"><?php _e('Free', TRS_DOMAIN); ?></h3>
								<p class="pricing__info-text" style="visibility: hidden;">
									<span data-pricing-text-type="annual">
										/ <?php _e('month, annually billed', TRS_DOMAIN); ?>
									</span>
									<span data-pricing-text-type="monthly" style="display: none;">
										/ <?php _e('month', TRS_DOMAIN); ?>
									</span>
								</p>
								<strong class="price-label pricing__info-label info-label--success"><?php _e('Always free, no time limit', TRS_DOMAIN); ?></strong>
							</div>
						</div>
						<div class="pricing__plan-header--footer">
							<div class="pricing__plan-header--footer-inner">
								<ul class="pricing__info">
									<li class="pricing__info-item">
										<div class="pricing__ingo_item-wrap_price">
											<strong>20</strong>
											<span class="pricing__info--value"><?php _e('Product Optimizations / day', TRS_DOMAIN); ?></span>
										</div>
									</li>
									<li class="pricing__info-item">
										<div class="pricing__ingo_item-wrap_price">
											<strong data-price-type="optimization">0.00€</strong>
											<span class="pricing__info--value"> / <?php _e('Product Optimization', TRS_DOMAIN); ?></span>
										</div>
										<span class="icon-circle-info" data-tooltip-content="<?php echo $tooltip_product_optimization_text; ?>"></span>
									</li>
									<li class="pricing__info-item">
										<div class="pricing__ingo_item-wrap_price">
											<strong data-price-type="on-demand-optimization">0.00€</strong>
											<span class="pricing__info--value"> / <?php _e('On-Demand Product Optimization', TRS_DOMAIN); ?></span>
										</div>
									</li>
								</ul>
								<a href="<?php SettingsPage::value('client_sellerlogic_register'); ?>" data-disable-if-more-than="20" class="cta-button pricing__button RP-upper-freemium-button"><?php _e('Get started', TRS_DOMAIN); ?></a>
							</div>
						</div>
					</div>
					<ul class="pricing__features">
						<li class="pricing__feature"><?php _e('All Amazon marketplaces', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('Event scheduler', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('Multi currency', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('B2C AI Repricing & Rule-based', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('B2B AI Repricing & Rule-based', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('Automatic Min & Max', TRS_DOMAIN); ?></li>
						<li class="pricing__feature">
							<?php _e('Product & Stock synchronization from Amazon', TRS_DOMAIN); ?>:
							<strong><?php printf(_n('every %d hour', 'every %d hours', 4, TRS_DOMAIN), 4); ?></strong>
						</li>
					</ul>
				</div>
				<div data-subscription-type="<?php echo SUBSCRIPTION_TYPE_STARTER; ?>" class="pricing__plan pricing__plan--starter">
					<div class="pricing__plan-header">
						<div class="pricing__plan-header--content">
							<div class="pricing__plan-header--inner-content">
								<h2 class="pricing__title"><?php _e('Starter', TRS_DOMAIN); ?></h2>
								<h3 data-price-type="month" class="pricing__price">0.00€</h3>
								<p class="pricing__info-text">
									<span data-pricing-text-type="annual">
										/ <?php _e('month, annually billed', TRS_DOMAIN); ?>
									</span>
									<span data-pricing-text-type="monthly" style="display: none;">
										/ <?php _e('month', TRS_DOMAIN); ?>
									</span>
								</p>
								<strong data-price-type="wrap-discount" class="price-label pricing__info-label info-label--success price-label--hidden">
									<?php _ex('Save', 'discount', TRS_DOMAIN); ?>
									<span data-price-type="discount"></span>
								</strong>
							</div>
						</div>
						<div class="pricing__plan-header--footer">
							<div class="pricing__plan-header--footer-inner">
								<ul class="pricing__info">
									<li class="pricing__info-item">
										<div class="pricing__ingo_item-wrap_price">
											<strong data-product-count="">0</strong>
											<span class="pricing__info--value"><?php _ex('optimized products / day', 'STARTER', TRS_DOMAIN); ?></span>
										</div>
									</li>
									<li class="pricing__info-item">
										<div class="pricing__ingo_item-wrap_price">
											<strong data-price-type="optimization">0.00€</strong>
											<span class="pricing__info--value"> / <?php _e('Product Optimization', TRS_DOMAIN); ?></span>
										</div>
										<span class="icon-circle-info" data-tooltip-content="<?php echo $tooltip_product_optimization_text; ?>"></span>
									</li>
									<li class="pricing__info-item">
										<div class="pricing__ingo_item-wrap_price">
											<strong data-price-type="on-demand-optimization">0.00€</strong>
											<span class="pricing__info--value"> / <?php _e('On-Demand Product Optimization', TRS_DOMAIN); ?></span>
										</div>
									</li>
								</ul>
								<a href="<?php SettingsPage::value('client_sellerlogic_register'); ?>" class="cta-button pricing__button RP-upper-starter-button"><?php _e('Get started', TRS_DOMAIN); ?></a>
							</div>
						</div>
					</div>
					<ul class="pricing__features">
						<li class="pricing__feature feature-plus--icon">
							<strong><?php _e('Everything in the Freemium plan, plus', TRS_DOMAIN); ?>:</strong></li>
						<li class="pricing__feature">
							<?php _e('Product & Stock synchronization from Amazon', TRS_DOMAIN); ?>:
							<strong><?php printf(_n('every %d hour', 'every %d hours', 2, TRS_DOMAIN), 2); ?></strong>
						</li>
						<li class="pricing__feature"><?php _e('Bulk editing of settings', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('Import operations', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('Export operations', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('Cost synchronization with Business Analytics', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('Dedicated Onboarding Specialist', TRS_DOMAIN); ?></li>
					</ul>
				</div>
				<div data-subscription-type="<?php echo SUBSCRIPTION_TYPE_ADVANCED; ?>" class="pricing__plan pricing__plan--advanced">
					<div class="pricing__plan-header">
						<div class="pricing__plan-header--content">
							<div class="pricing__plan-header--inner-content">
								<h2 class="pricing__title">
									<?php _e('Advanced', TRS_DOMAIN); ?>
									<span class="pricing__plan__mark"><?php _e('Best value', TRS_DOMAIN); ?></span>
								</h2>
								<h3 data-price-type="month" class="pricing__price">0.00€</h3>
								<p class="pricing__info-text">
									<span data-pricing-text-type="annual">
										/ <?php _e('month, annually billed', TRS_DOMAIN); ?>
									</span>
									<span data-pricing-text-type="monthly" style="display: none;">
										/ <?php _e('month', TRS_DOMAIN); ?>
									</span>
								</p>
								<strong data-price-type="wrap-discount" class="price-label pricing__info-label info-label--success price-label--hidden">
									<?php _ex('Save', 'discount', TRS_DOMAIN); ?>
									<span data-price-type="discount"></span>
								</strong>
							</div>
						</div>
						<div class="pricing__plan-header--footer">
							<div class="pricing__plan-header--footer-inner">
								<ul class="pricing__info">
									<li class="pricing__info-item">
										<div class="pricing__ingo_item-wrap_price">
											<strong data-product-count="">0</strong>
											<span class="pricing__info--value"><?php _ex('optimized products / day', 'ADVANCED', TRS_DOMAIN); ?></span>
										</div>
									</li>
									<li class="pricing__info-item">
										<div class="pricing__ingo_item-wrap_price">
											<strong data-price-type="optimization">0.00€</strong>
											<span class="pricing__info--value"> / <?php _e('Product Optimization', TRS_DOMAIN); ?></span>
										</div>
										<span class="icon-circle-info" data-tooltip-content="<?php echo $tooltip_product_optimization_text; ?>"></span>
									</li>
									<li class="pricing__info-item">
										<div class="pricing__ingo_item-wrap_price">
											<strong data-price-type="on-demand-optimization">0.00€</strong>
											<span class="pricing__info--value"> / <?php _e('On-Demand Product Optimization', TRS_DOMAIN); ?></span>
										</div>
									</li>
								</ul>
								<a href="<?php SettingsPage::value('client_sellerlogic_register'); ?>" class="cta-button pricing__button RP-upper-advanced-button"><?php _e('Get started', TRS_DOMAIN); ?></a>
							</div>
						</div>
					</div>
					<ul class="pricing__features">
						<li class="pricing__feature feature-plus--icon">
							<strong><?php _e('Everything in the Starter plan, plus', TRS_DOMAIN); ?>:</strong></li>
						<li class="pricing__feature">
							<?php _e('Product & Stock synchronization from Amazon', TRS_DOMAIN); ?>:
							<strong><?php _e('hourly', TRS_DOMAIN); ?></strong>
						</li>
						<li class="pricing__feature"><?php _e('API', TRS_DOMAIN); ?></li>
						<li class="pricing__feature"><?php _e('User permissions', TRS_DOMAIN); ?></li>
					</ul>
				</div>
			</div>
			<div id="compare-plans-table" class="compare-plans-table">
				<details open>
					<summary><h2 class="compare-plans-table__title"><?php _e('Compare plans', TRS_DOMAIN); ?></h2></summary>
					<div class="table-wrapper">
						<table>
							<thead>
							<tr>
								<th class="feature-name"><?php _e('Features', TRS_DOMAIN); ?></th>
								<th><?php _e('Trial', TRS_DOMAIN); ?></th>
								<th><?php _e('Freemium', TRS_DOMAIN); ?></th>
								<th><?php _e('Starter', TRS_DOMAIN); ?></th>
								<th><?php _e('Advanced', TRS_DOMAIN); ?></th>
							</tr>
							</thead>
							<tbody>
							<tr>
								<td class="feature-name">
									<a href="<?php echo get_kb_link('All Amazon marketplaces'); ?>" target="_blank"><?php _e('All Amazon marketplaces', TRS_DOMAIN); ?></a>
								</td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
							</tr>
							<tr>
								<td class="feature-name">
									<a href="<?php echo get_kb_link('Event scheduler'); ?>" target="_blank"><?php _e('Event scheduler', TRS_DOMAIN); ?></a>
								</td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
							</tr>
							<tr>
								<td class="feature-name">
									<a href="<?php echo get_kb_link('Multi currency'); ?>" target="_blank"><?php _e('Multi currency', TRS_DOMAIN); ?></a>
								</td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
							</tr>
							<tr>
								<td class="feature-name">
									<a href="<?php echo get_kb_link('B2C AI Repricing & Rule-based'); ?>" target="_blank"><?php _e('B2C AI Repricing & Rule-based', TRS_DOMAIN); ?></a>
								</td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
							</tr>
							<tr>
								<td class="feature-name">
									<a href="<?php echo get_kb_link('B2B AI Repricing & Rule-based'); ?>" target="_blank"><?php _e('B2B AI Repricing & Rule-based', TRS_DOMAIN); ?></a>
								</td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
							</tr>
							<tr>
								<td class="feature-name">
									<a href="<?php echo get_kb_link('Automatic Min & Max'); ?>" target="_blank"><?php _e('Automatic Min & Max', TRS_DOMAIN); ?></a>
								</td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
							</tr>
							<tr>
								<td class="feature-name">
									<a href="<?php echo get_kb_link('Product & Stock synchronization from Amazon'); ?>" target="_blank"><?php _e('Product & Stock synchronization from Amazon', TRS_DOMAIN); ?></a>
								</td>
								<td><?php printf(_n('every %d hour', 'every %d hours', 2, TRS_DOMAIN), 2); ?></td>
								<td><?php printf(_n('every %d hour', 'every %d hours', 4, TRS_DOMAIN), 4); ?></td>
								<td><?php printf(_n('every %d hour', 'every %d hours', 2, TRS_DOMAIN), 2); ?></td>
								<td><?php _e('hourly', TRS_DOMAIN); ?></td>
							</tr>
							<tr>
								<td class="feature-name">
									<a href="<?php echo get_kb_link('Bulk editing of settings'); ?>" target="_blank"><?php _e('Bulk editing of settings', TRS_DOMAIN); ?></a>
								</td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-disabled--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
							</tr>
							<tr>
								<td class="feature-name">
									<a href="<?php echo get_kb_link('Import operations'); ?>" target="_blank"><?php _e('Import operations', TRS_DOMAIN); ?></a>
								</td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-disabled--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
							</tr>
							<tr>
								<td class="feature-name">
									<a href="<?php echo get_kb_link('Export operations'); ?>" target="_blank"><?php _e('Export operations', TRS_DOMAIN); ?></a>
								</td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-disabled--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
							</tr>
							<tr>
								<td class="feature-name">
									<a href="<?php echo get_kb_link('Cost synchronization with Business Analytics'); ?>" target="_blank"><?php _e('Cost synchronization with Business Analytics', TRS_DOMAIN); ?></a>
								</td>
								<td><i class="icon feature-disabled--icon"></i></td>
								<td><i class="icon feature-disabled--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
							</tr>
							<tr>
								<td class="feature-name">
									<a href="<?php echo get_kb_link('Dedicated Onboarding Specialist'); ?>" target="_blank"><?php _e('Dedicated Onboarding Specialist', TRS_DOMAIN); ?></a>
								</td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-disabled--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
							</tr>
							<tr>
								<td class="feature-name">
									<a href="<?php echo get_kb_link('API'); ?>" target="_blank"><?php _e('API', TRS_DOMAIN); ?></a>
								</td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-disabled--icon"></i></td>
								<td><i class="icon feature-disabled--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
							</tr>
							<tr>
								<td class="feature-name">
									<a href="<?php echo get_kb_link('User permissions'); ?>" target="_blank"><?php _e('User permissions', TRS_DOMAIN); ?></a>
								</td>
								<td><i class="icon feature-success--icon"></i></td>
								<td><i class="icon feature-disabled--icon"></i></td>
								<td><i class="icon feature-disabled--icon"></i></td>
								<td><i class="icon feature-success--icon"></i></td>
							</tr>
							<tr class="table-footer">
								<td class="feature-name"></td>
								<td>
									<a href="<?php SettingsPage::value('client_sellerlogic_register'); ?>" class="cta-button RP-comparison-trial-button"><?php _e('Get started', TRS_DOMAIN); ?></a
								</td>
								<td>
									<a href="<?php SettingsPage::value('client_sellerlogic_register'); ?>" class="cta-button RP-comparison-freemium-button"><?php _e('Get started', TRS_DOMAIN); ?></a
								</td>
								<td>
									<a href="<?php SettingsPage::value('client_sellerlogic_register'); ?>" class="cta-button RP-comparison-starter-button"><?php _e('Get started', TRS_DOMAIN); ?></a
								</td>
								<td>
									<a href="<?php SettingsPage::value('client_sellerlogic_register'); ?>" class="cta-button RP-comparison-advanced-button"><?php _e('Get started', TRS_DOMAIN); ?></a
								</td>
							</tr>
							</tbody>
						</table>
					</div>
				</details>
			</div>
		</div>
	</div>

<?php }, 'price-calculator');

/**
 * Get KB article link based on the current language
 *
 * @param string $key The key of the article in the KB_ARTICLE_LINKS array
 *
 * @return string|null The URL for the current language or null if not found
 */
function get_kb_link(string $key): ?string{
	$current_language = apply_filters('wpml_current_language', NULL);

	if(isset(KB_ARTICLE_LINKS[$key])){
		if(isset(KB_ARTICLE_LINKS[$key][$current_language])){
			return KB_ARTICLE_LINKS[$key][$current_language];
		}elseif(isset(KB_ARTICLE_LINKS[$key]['en'])){
			return KB_ARTICLE_LINKS[$key]['en'];
		}
	}

	return null;
}
