import './index.scss';
import _ from 'lodash';
import {isSmallMobile} from '../../../../../../_src/js/modules/mediaQuery';
import getTippy from '../../../../../../_src/js/modules/tooltipTippy';

const SUBSCRIPTION_TYPE_TRIAL = 'TRIAL';
const SUBSCRIPTION_TYPE_FREEMIUM = 'FREEMIUM';
const SUBSCRIPTION_TYPE_STARTER = 'STARTER';
const SUBSCRIPTION_TYPE_ADVANCED = 'ADVANCED';

async function runCalculator(){
	const [{default: config}, {RestApiResult}, {PriceFormatter}, {default: noUiSlider}] = await Promise.all([
		import('./config/rest-api-result.json'),
		import('./config/RestApiResult.js'),
		import('./priceFormatter/PriceFormatter.js'),
		import('nouislider'),
		import('nouislider/dist/nouislider.css'),
	]);

	const priceFormatter = new PriceFormatter({
		currency: " €",
		minDecimals: 2,
		maxDecimals: 2,
		decimalSeparator: ",",
		thousandSeparator: ".",
	});
	const longPriceFormatter = new PriceFormatter({
		currency: " €",
		minDecimals: 2,
		maxDecimals: 5,
		decimalSeparator: ",",
		thousandSeparator: ".",
	});

	const restApiResult = new RestApiResult(config);
	const trialSubscription = restApiResult.getSubscriptionByType(SUBSCRIPTION_TYPE_TRIAL);
	const freemiumSubscription = restApiResult.getSubscriptionByType(SUBSCRIPTION_TYPE_FREEMIUM);
	const starterSubscription = restApiResult.getSubscriptionByType(SUBSCRIPTION_TYPE_STARTER);
	const advancedSubscription = restApiResult.getSubscriptionByType(SUBSCRIPTION_TYPE_ADVANCED);
	const subscriptionTypes = {
		[SUBSCRIPTION_TYPE_TRIAL]: trialSubscription,
		[SUBSCRIPTION_TYPE_FREEMIUM]: freemiumSubscription,
		[SUBSCRIPTION_TYPE_STARTER]: starterSubscription,
		[SUBSCRIPTION_TYPE_ADVANCED]: advancedSubscription,
	};

	const blockCalculators = document.querySelectorAll(".theme-block-price-calculator");

	blockCalculators.forEach(function(blockCalculator){
		const oneYearCheckbox = blockCalculator.querySelector('[name="one_year_checkbox"]');
		const slider = blockCalculator.querySelector('.js-price-range');
		const valuesForSlider = restApiResult.getSizes();

		const format = {
			to: function(value){
				return valuesForSlider[Math.round(value)];
			},
			from: function(value){
				return valuesForSlider.indexOf(Number(value));
			},
		};

		const stepsLinesValues = generateEvenlySpacedNumbers(0, valuesForSlider.length - 1, 86 / 2);
		const stepsLines = new Set(stepsLinesValues);
		const stepsNumbers = new Set(generateEvenlySpacedNumbers(0, valuesForSlider.length - 1, 9, stepsLinesValues));

		const piptsDefault = {
			mode: 'count',
			format: format,
			values: valuesForSlider.length,
			filter: function filterPips(value, type){
				value = Math.round(value);
				return stepsNumbers.has(value)? 1 : (stepsLines.has(value)? 0 : -1);
			},
		};

		const api = noUiSlider.create(slider, {
			start: 20,
			range: {min: 0, max: valuesForSlider.length - 1},
			format: format,
			margin: 20,
			step: 1,
			pips: piptsDefault,
			connect: [true, false],
		});

		const updatePips = function(){
			const memIsSmallMobile = isSmallMobile();
			if(memIsSmallMobile){
				api.updateOptions({
					pips: {
						density: 3,
					},
				});
			}else{
				api.updateOptions({
					pips: {
						...piptsDefault,
					},
				});
			}
		};

		// update pips
		updatePips();
		window.addEventListener('resize', _.debounce(updatePips), 100);

		api.on('update', function(values, handle){
			renderCalculatorPrices(blockCalculator, api);
		});

		const onChangeOneYearCheckbox = function(){
			const oneYearCheckboxChecked = blockCalculator.querySelector('[name="one_year_checkbox"]').checked;
			const annualPricingTextElements = blockCalculator.querySelectorAll('[data-pricing-text-type="annual"]');
			const monthlyPricingTextElements = blockCalculator.querySelectorAll('[data-pricing-text-type="monthly"]');
			const annualPricingHideElements = blockCalculator.querySelectorAll('[data-pricing-hide-when-type="annual"]');
			const monthlyPricingHideElements = blockCalculator.querySelectorAll('[data-pricing-hide-when-type="monthly"]');

			annualPricingTextElements.forEach(function(element){
				element.style.display = oneYearCheckboxChecked? 'block' : 'none';
			});

			monthlyPricingTextElements.forEach(function(element){
				element.style.display = oneYearCheckboxChecked? 'none' : 'block';
			});

			annualPricingHideElements.forEach(function(element){
				element.style.display = oneYearCheckboxChecked? 'none' : 'block';
			});

			monthlyPricingHideElements.forEach(function(element){
				element.style.display = oneYearCheckboxChecked? 'block' : 'none';
			});

			renderCalculatorPrices(blockCalculator, api);
			setMinHeightCardHeader(blockCalculator);
		};
		onChangeOneYearCheckbox();
		setMinHeightCardHeader(blockCalculator);
		oneYearCheckbox.addEventListener('change', onChangeOneYearCheckbox);
		window.addEventListener('resize', _.debounce(() => {
			setMinHeightCardHeader(blockCalculator);
		}, 400));
	});

	function setMinHeightCardHeader(blockCalculator){
		const cards = blockCalculator.querySelectorAll('.pricing__plan');
		const cardsFooter = blockCalculator.querySelectorAll('.pricing__plan-header--footer-inner');
		const cardsHeader = blockCalculator.querySelectorAll('.pricing__plan-header--inner-content');
		const contentMaxHeight = Math.max(...Array.from(cardsHeader).map(cardHeader => cardHeader.offsetHeight));
		const footerMaxHeight = Math.max(...Array.from(cardsFooter).map(cardFooter => cardFooter.offsetHeight));
		cards.forEach(function(card){
			const parentCardHeader = card.querySelector('.pricing__plan-header--content');
			const parentCardFooter = card.querySelector('.pricing__plan-header--footer');
			parentCardHeader.style.minHeight = contentMaxHeight + 'px';
			parentCardFooter.style.minHeight = footerMaxHeight + 'px';
		});
	}

	/**
	 * @function renderCalculatorPrices
	 * @description renders the prices of all calculator subscription types based on the current slider value
	 * @param {HTMLElement} blockCalculator - the element that contains all the subscription types
	 * @param {noUiSlider} sliderApi - the slider api
	 */
	function renderCalculatorPrices(blockCalculator, sliderApi){
		const amountElement = blockCalculator.querySelector('.theme-block-price-calculator__amount');
		const oneYearCheckboxChecked = blockCalculator.querySelector('[name="one_year_checkbox"]').checked;
		const countedMonths = oneYearCheckboxChecked? 10 : 12; //Discount

		const sliderValue = sliderApi.get();
		const sliderValueFormatted = sliderValue.toLocaleString();
		amountElement.innerHTML = sliderValueFormatted;

		for(const subscriptionType in subscriptionTypes){
			const subscription = subscriptionTypes[subscriptionType];
			const matrix = subscription.getMatrixEntryBySize(sliderValue);
			const priceForOneMonth = matrix.getCalculatedYearPriceForOneMonth(countedMonths);
			const size = matrix.getSize();
			const priceOptimizationForOneUnit = matrix.getCalculatedPriceOptimizationForOneUnit(countedMonths);
			const priceDiscountForOneMonth = matrix.getCalculatedPriceDiscountForOneMonth(countedMonths);
			const onDemandOptimization = matrix.getPricePerUnit();

			const subscriptionCard = blockCalculator.querySelector(`[data-subscription-type="${subscriptionType}"]`);
			if(!subscriptionCard){
				continue;
			}

			const productCountElement = subscriptionCard.querySelector('[data-product-count]');
			if(productCountElement){
				productCountElement.innerHTML = size.toLocaleString();
			}

			const priceMonthElement = subscriptionCard.querySelector('[data-price-type="month"]');
			if(priceMonthElement){
				priceMonthElement.innerHTML = priceFormatter.format(priceForOneMonth);
			}

			const priceYearElement = subscriptionCard.querySelector('[data-price-type="year"]');
			if(priceYearElement){
				priceYearElement.innerHTML = priceFormatter.format(priceForOneMonth * 12);
			}

			const priceOptimizationElement = subscriptionCard.querySelector('[data-price-type="optimization"]');
			if(priceOptimizationElement){
				priceOptimizationElement.innerHTML = longPriceFormatter.format(priceOptimizationForOneUnit);
			}

			const onDemandOptimizationElement = subscriptionCard.querySelector('[data-price-type="on-demand-optimization"]');
			if(onDemandOptimizationElement){
				onDemandOptimizationElement.innerHTML = longPriceFormatter.format(onDemandOptimization);
			}

			const wrapDiscount = subscriptionCard.querySelector('[data-price-type="wrap-discount"]');
			const discount = subscriptionCard.querySelector('[data-price-type="discount"]');
			if(discount){
				if(priceDiscountForOneMonth){
					discount.innerHTML = priceFormatter.format(priceDiscountForOneMonth);
					if(wrapDiscount){
						wrapDiscount.classList.remove('price-label--hidden');
					}
				}else{
					if(wrapDiscount){
						wrapDiscount.classList.add('price-label--hidden');
					}
				}
			}

			const disableIfMoreThanElement = subscriptionCard.querySelector('[data-disable-if-more-than]');
			if(disableIfMoreThanElement){
				const disableIfMoreThan = disableIfMoreThanElement.dataset.disableIfMoreThan;
				if(sliderValue > disableIfMoreThan){
					disableIfMoreThanElement.classList.add('disabled');
				}else{
					disableIfMoreThanElement.classList.remove('disabled');
				}
			}
		}
	}
}

function generateEvenlySpacedNumbers(start, end, count, values = []){
	if(values.length > 0){
		const step = (end - start) / (count - 1);
		const result = [];

		for(let i = 0; i < count; i++){
			const value = start + step * i;
			const closestValue = values.reduce((prev, curr) => {
				return (Math.abs(curr - value) < Math.abs(prev - value)? curr : prev);
			});
			result.push(closestValue);
		}

		return result;
	}

	const step = (end - start) / (count - 1);
	const result = [];

	for(let i = 0; i < count; i++){
		const value = start + step * i;
		result.push(Number(Math.floor(value)));
	}

	return result;
}

async function runTooltip(){
	const {tippy} = await getTippy();

	tippy('[data-tooltip-content]', {
		placement: 'top',
		content(reference){
			return reference.getAttribute('data-tooltip-content');
		},
		allowHTML: true,
	});
}

document.addEventListener('DOMContentLoaded', function(){
	if(document.querySelector('.theme-block-price-calculator')){
		runCalculator();
		runTooltip();
	}
});
