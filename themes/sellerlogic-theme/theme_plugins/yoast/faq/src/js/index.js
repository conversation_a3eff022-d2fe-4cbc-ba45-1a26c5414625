import '../css/index.scss';

document.addEventListener('DOMContentLoaded', function(){
	const faqAnswers = document.querySelectorAll('.wp-block-yoast-faq-block .schema-faq-section .schema-faq-answer');

	document.addEventListener('click', function(e){
		const target = e.target.closest('.wp-block-yoast-faq-block .schema-faq-section .schema-faq-question');
		if(target){
			const parent = target.closest('.schema-faq-section');
			const answer = parent.querySelector('.schema-faq-answer');

			if(answer){
				if(answer.style.display === 'none' || !answer.style.display){
					answer.style.display = 'block';
					const height = answer.scrollHeight + 'px';
					answer.style.height = height;
					answer.classList.add('expanded');
				}else{
					answer.classList.remove('expanded');
					answer.style.height = '0';
					answer.addEventListener('transitionend', function handleTransitionEnd(){
						answer.style.display = 'none';
						answer.removeEventListener('transitionend', handleTransitionEnd);
					});
				}
			}

			parent.classList.toggle('active');
		}
	});

	const hash = location.hash;
	if(hash && !hash.match(/^#\d+/)){
		const currentQuestion = document.querySelector(`.wp-block-yoast-faq-block ${hash} .schema-faq-question`);
		if(currentQuestion){
			currentQuestion.click();
		}
	}
});
