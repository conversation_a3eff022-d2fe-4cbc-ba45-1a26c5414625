<?php
class TCP_Admin {
    public function __construct() {
        add_action('admin_menu', [$this, 'add_admin_page']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_scripts']);
        add_action('wp_ajax_tcp_parse_channel', [$this, 'ajax_parse_channel']);
    }

    public function add_admin_page() {
        add_menu_page(
            'Telegram Parser',
            'Telegram Parser',
            'manage_options',
            'telegram-channel-parser',
            [$this, 'render_admin_page'],
            'dashicons-format-chat'
        );
    }

    public function render_admin_page() {
        ?>
        <div class="wrap">
            <h1>Парсер Telegram-каналов</h1>
            <div class="card">
                <h2>Ручной парсинг</h2>
                <code>public % wp telegram-parser parse @banksta --limit=10</code>
                <form id="tcp-parser-form">
                    <p>
                        <label for="tcp-channel">Название канала:</label>
                        <input type="text" id="tcp-channel" name="channel" placeholder="@durov" required>
                    </p>
                    <p>
                        <label for="tcp-limit">Лимит постов:</label>
                        <input type="number" id="tcp-limit" name="limit" value="5" min="1" max="50">
                    </p>
                    <button type="submit" class="button button-primary">Запустить парсинг</button>
                </form>
                <div id="tcp-results"></div>
            </div>
        </div>
        <?php
    }

    public function ajax_parse_channel() {
        check_ajax_referer('tcp_ajax_nonce');

        $channel = sanitize_text_field($_POST['channel']);
        $limit = intval($_POST['limit']);

        $parser = new TCP_Parser($channel);
        $posts = $parser->get_last_posts($limit);

        if (is_wp_error($posts)) {
            wp_send_json_error($posts->get_error_message());
        }

        $saved_count = 0;
        foreach ($posts as $post) {
            $post['channel'] = $channel;
            if (TCP_Database::save_post($post)) {
                $saved_count++;
            }
        }

        wp_send_json_success("Сохранено постов: {$saved_count}");
    }

    public function enqueue_scripts($hook) {
        if ($hook !== 'toplevel_page_telegram-channel-parser') return;

        wp_enqueue_script(
            'tcp-admin-js',
            TCP_PLUGIN_URL . 'assets/js/admin.js',
            ['jquery'],
            '1.1',
            true
        );

        wp_localize_script('tcp-admin-js', 'tcp_ajax', [
            'url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('tcp_ajax_nonce')
        ]);
    }
}