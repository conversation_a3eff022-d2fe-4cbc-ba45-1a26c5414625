<?php
/**
 * 
 * For Api OpenAI
 */

class TCP_API_Provider {

    private const API_KEY = '************************************************************************************************************************************';
    private const ASSISTANT_ID = 'asst_RLnRqlIdTBPoRygXPm5okkRG';

    private const LOOP_MAX_ATTEMPTS = 20;   
    private const REQUEST_TIMEOUT = 120; 
    
    private const THREADS_API_URL =  'https://api.openai.com/v1/threads';
    private const MESSAGES_API_URL = 'https://api.openai.com/v1/threads/{thread_id}/messages?run_id={run_id}';
    private const RUNS_API_URL =     'https://api.openai.com/v1/threads/{thread_id}/runs';
    private const RUN_STATUS_URL =   'https://api.openai.com/v1/threads/{thread_id}/runs/{run_id}';

    private ?string $threadId;
    private int $retryCount;
    private string $prompt;

    public function __construct($prompt)
    { 
        if (empty(self::API_KEY) || empty(self::ASSISTANT_ID)) {
            print_r("❌ API key and assistant ID are required.\n");
            throw new \Exception("API key and assistant ID are required.");
        }
        else {
            print_r("✅ API key and assistant ID are set.\n");
        }

        $this->prompt = $prompt;
        $this->retryCount = 0;
        $this->threadId = null;
    }   

    public function sendMessage(string $text)
    {

       print_r(date('Y-m-d H:i:s') . " 💬 " . $text . "\n");


        try {
            if ($this->threadId === null || $this->retryCount > 50) {  // Create new thread each 50 messages due to context brokes
                $this->threadId = $this->createThread();
                $this->retryCount = 0; 
            }
            
            sleep(2); 

            $this->addMessageToThread($this->threadId, $text);
            $runId = $this->createRun($this->threadId);
            $this->pollRunStatus($this->threadId, $runId);
            $this->retryCount++; 
            return $this->getMessageFromThread($this->threadId, $runId, $text);
    
        } catch (\Throwable $e) {
            print_r(date('Y-m-d H:i:s') . "❌ Task failed: " . $e->getMessage() . "\n");
            $this->threadId = null;
            $this->retryCount = 0;
            sleep(3);
            return $this->sendMessage($text);
        }
    }

    private function pollRunStatus(string $threadId, string $runId): void {
        
        $startTime = time();
    
        while (time() - $startTime < self::REQUEST_TIMEOUT) {
            $status = $this->getRunStatus($threadId, $runId);
            print_r(date('Y-m-d H:i:s') . " ⏳ Polling status: $status\n");
    
            if ($status === 'completed') {
                return; 
            }
    
            if (in_array($status, ['failed', 'cancelled', 'expired'])) {
                throw new \Exception("Run $runId failed with status: $status");
                print_r(date('Y-m-d H:i:s') . "❌ Run $runId failed with status: $status\n");
            }
    
            sleep(3); 
        }
    
        throw new \Exception("Timeout waiting for run $runId to complete.");
        print_r(date('Y-m-d H:i:s') . "❌ Timeout waiting for run $runId to complete.\n");
    }

    private function getRunStatus(string $threadId, string $runId): ?string {

        $url = str_replace(['{thread_id}', '{run_id}'], [$threadId, $runId], self::RUN_STATUS_URL);
    
        $response = wp_remote_get($url, [
            'headers' => [
                'Authorization' => 'Bearer ' . self::API_KEY,
                'OpenAI-Beta'   => 'assistants=v2',
            ],
            'timeout' => self::REQUEST_TIMEOUT,
        ]);
    
        if (is_wp_error($response)) {
            print_r(date('Y-m-d H:i:s') . "❌ Error polling run status: " . $response->get_error_message() . "\n");
            return null;
        }
    
        $body = json_decode(wp_remote_retrieve_body($response), true);
        return $body['status'] ?? null;
    }

    public function executeRequest(string $method, string $url, array $data = []): array
    {
        $attempt = 0;
        $response = [];

        while ($attempt < self::LOOP_MAX_ATTEMPTS) {
            sleep(2);
            $response = $this->sendRequest($method, $url, $data);
            if (!empty($response) && empty($response['error'])) {
                return $response;
            }
            $attempt++;
            $error = "Attempt {$attempt} for {$url} failed. Response: " . json_encode($response);
            print_r(date('Y-m-d H:i:s') . "❌ " . $error . "\n");
        }
        return $response;
    }

    public function createThread()
    {
        $data = [
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $this->prompt,
                ],
            ],
        ];


        print_r(date('Y-m-d H:i:s') . " ℹ️ {$data['messages'][0]['content']}\n");

        $response = $this->executeRequest('POST', self::THREADS_API_URL, $data);

        if (empty($response['id'])) {
            print_r("⚠️ Failed to create thread. Response: " . json_encode($response));
        }
        return $response['id'];
    }

    public function addMessageToThread(string $threadId, string $text): void
    {
        $url = str_replace('{thread_id}', $threadId, self::MESSAGES_API_URL);
        
        $this->executeRequest('POST', $url, [
            'role' => 'user',
            'content' => $text,
        ]);
    }
    
    public function createRun(string $threadId): string
    {
        $url = str_replace('{thread_id}', $threadId, self::RUNS_API_URL);
        $data = ['assistant_id' => self::ASSISTANT_ID];
    
        for ($attempt = 1; $attempt <= self::LOOP_MAX_ATTEMPTS; $attempt++) {
            $response = $this->executeRequest('POST', $url, $data);
            $runId = $response['id'] ?? '';
    
            if (!empty($runId)) {
                return $runId;
            }
    
            $error = "Failed to create run (attempt {$attempt}). Response: " . json_encode($response);
            print_r(date('Y-m-d H:i:s') . "❌ {$error}\n");
            sleep(2);
        }
    
        $error = date('Y-m-d H:i:s') . "❌ Exceeded max attempts in createRun for thread {$threadId}.";
        print_r("{$error}\n");
        return '';
    }

    public function getMessageFromThread(string $threadId, string $runId, string $text, int $retryCount = 0): string
    {
        $url = str_replace(['{thread_id}', '{run_id}'], [$threadId, $runId], self::MESSAGES_API_URL);
    
        for ($attempt = 1; $attempt <= self::LOOP_MAX_ATTEMPTS; $attempt++) {
            $response = $this->executeRequest('GET', $url);
            
            $message = $response['data'][0]['content'][0]['text']['value'] ?? '';
    
            sleep(2);

            if (!empty($message)) {
                print_r(date('Y-m-d H:i:s') . " 🤖 {$message}\n");
                return $message;
            }
            sleep(2);
            print_r(date('Y-m-d H:i:s') . " ❌ Attempt {$attempt} failed to fetch message. Response: " . json_encode($response) . "\n");
        }
    
        if ($retryCount < self::LOOP_MAX_ATTEMPTS) {
            print_r(date('Y-m-d H:i:s') . "⚠️ Retrying with new thread. Retry count: {$retryCount}.\n");
    
            $newThreadId = $this->createThread();
            $this->addMessageToThread($newThreadId, $text);
            $newRunId = $this->createRun($newThreadId);
    
            if (!empty($newRunId)) {
                $this->pollRunStatus($newThreadId, $newRunId);
            }
    
            return $this->getMessageFromThread($newThreadId, $newRunId, $text, $retryCount + 1);
        }
    
        print_r(date('Y-m-d H:i:s') . "❌ Maximum retries ({$retryCount}) reached. Returning original text.\n");
        return $text;
    }
    
    public function sendRequest(string $method, string $url, array $data = []): array
    {

        $api_key = self::API_KEY;

        $args = [
            'headers' => [
                'Authorization' => "Bearer {$api_key}",
                'Content-Type'  => 'application/json',
                'OpenAI-Beta'   => 'assistants=v2',
            ],
            'timeout' => self::REQUEST_TIMEOUT,
        ];
    
        if ($method === 'POST') {
            $args['body'] = json_encode($data);
        }
    
        $response = $method === 'GET' ? wp_remote_get($url, $args) : wp_remote_post($url, $args);
    
        if (is_wp_error($response)) {
            $errorMessage = "❌ Request to {$url} failed. Error: " . $response->get_error_message();
            print_r(date('Y-m-d H:i:s') . "❌ {$errorMessage}\n");
            return [];
        }
    
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $decodedResponse = json_decode($body, true);
    
       
        if ($status_code === 400) {
            $errorDetails = $decodedResponse['error']['message'] ?? 'No additional error details';
            print_r(date('Y-m-d H:i:s') . "❌ Bad Request (400) - creating new thread: {$errorDetails}\n");

            $this->threadId = null; 
            $this->retryCount = 0;
            return [];
        }
    
        if ($status_code === 429) {
            print_r(date('Y-m-d H:i:s') . "❌ Rate limited (429). Sleeping 10s...\n");
            sleep(5);
            return [];
        }
        
      
        if (!in_array($status_code, [200, 201, 204])) {
            print_r(date('Y-m-d H:i:s') . "❌ API request failed with status {$status_code}\n");
            return [];
        }
    
        return $decodedResponse ?: [];
    }

}