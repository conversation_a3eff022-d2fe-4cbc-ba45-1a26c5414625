<?php

if (!class_exists('WP_CLI')) return;

class TCP_CLI_Command extends WP_CLI_Command {
    /**
     * Парсит Telegram-канал и сохраняет посты
     *
     * ## OPTIONS
     * <channel>
     * : Название канала (например, @durov)
     *
     * [--limit=<limit>]
     * : Лимит постов (по умолчанию: 10)
     *
     * [--proxy=<proxy>]
     * : Прокси (опционально)
     *
     * [--delay=<delay>]
     * : Задержка между запросами в секундах (по умолчанию: 1)
     */
    public function parse($args, $assoc_args) {
        list($channel) = $args;
        $limit = $assoc_args['limit'] ?? 10;
        $proxy = $assoc_args['proxy'] ?? '';
        $delay = $assoc_args['delay'] ?? 1;

        WP_CLI::line("Парсинг канала {$channel}...");

        $parser = new TCP_Parser($channel, $proxy);
        $posts = $parser->get_last_posts($limit);

        print_r("Posts number: " . count($posts) . "\n");

        if (is_wp_error($posts)) {
            WP_CLI::error($posts->get_error_message());
            return;
        }

       // $progress = WP_CLI\Utils\make_progress_bar('Обработка', count($posts));

        $gpt_chat = new TCP_API_Provider('
            Rewrite the following cooking text in Russian to make it sound more natural, lively, and written by a real person, while keeping the original meaning. 
            Preserve the formatting exactly as in the original (line breaks, bullet points, spacing, emojis, hashtags, etc.). 
            Do NOT translate the text into another language. If rewriting is not possible, return the original as-is.
            Remove hashtags and links.
            If you detect that the text is an ad - return nothing, empty response.
        ');


        foreach ($posts as $post) {
            $post['channel'] = $channel;

            // Проверяем, существует ли уже пост с таким message_id
            $existing_posts = get_posts([
                'meta_key' => 'tcp_message_id',
                'meta_value' => $post['id'],
                'post_type' => 'post',
                'post_status' => 'any',
                'numberposts' => 1
            ]);

            if (!empty($existing_posts)) {
                print_r("Post with ID " . $post['id'] . " already exists (WordPress ID: " . $existing_posts[0]->ID . ")\n");
                continue;
            }


            $post['text'] = $gpt_chat->sendMessage($post['text']);
            TCP_Database::save_post($post);
        }

        //$progress->finish();
        WP_CLI::success("Готово!");
    }
}

WP_CLI::add_command('telegram-parser', 'TCP_CLI_Command');



//wp telegram-parser parse @channel --limit=5