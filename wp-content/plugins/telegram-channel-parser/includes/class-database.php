<?php
// Подключаем класс для очистки контента
require_once(plugin_dir_path(__FILE__) . 'class-content-modifier.php');


class TCP_Database {
    public static function activate() {
        // При активации плагина
    }

    public static function deactivate() {
        // При деактивации
    }

    public static function save_post($post_data) {
        print_r("\n");
        print_r("Processing post: " . $post_data['id'] . "\n");

        if($post_data['text'] == ''){
            print_r("Post is empty. Skipping...\n");
            return false;
        }
        
        // Очищаем текст от упоминаний (@username)
        $cleaned_text = TCP_Content_Modifier::remove_mentions($post_data['text']);
        $cleaned_title = TCP_Content_Modifier::clean_title($cleaned_text, 100);

        // Преобразуем переносы строк в HTML-формат для правильного отображения
        $formatted_content = self::format_text_for_wordpress($cleaned_text);

        // Создаем новый пост только с очищенным текстом
        $post_id = wp_insert_post([
            'post_title' => !empty($cleaned_title) ? $cleaned_title : wp_trim_words($cleaned_text, 10, '...'),
            'post_content' => $formatted_content, // Форматированный текст с HTML переносами
            'post_status' => 'publish',
            'post_type' => 'post',
        ]);

        if (is_wp_error($post_id) || !$post_id) {
            error_log('TCP Error: Failed to create post');
            return false;
        }

        // Сохраняем метаданные
        update_post_meta($post_id, 'tcp_message_id', $post_data['id']);
        update_post_meta($post_id, 'tcp_channel', $post_data['channel']);

        // Обрабатываем медиа файлы и обновляем контент
        self::process_media($post_data, $post_id);

        return $post_id;
    }

    private static function process_media($post_data, $post_id) {
        if (empty($post_data['media'])) {
            return;
        }

        $media = $post_data['media'];
        $uploaded_media_ids = [];
        $processed_urls = []; // Отслеживаем уже обработанные URL

        // Обрабатываем изображения
        if (!empty($media['images'])) {
            foreach ($media['images'] as $image_url) {
                if (in_array($image_url, $processed_urls)) {
                    continue; // Пропускаем дубли
                }
                $processed_urls[] = $image_url;

                print_r('Uploading image: ' . $image_url . "\n");
                $media_id = self::upload_media($image_url, $post_id);

                if ($media_id) {
                    $uploaded_media_ids[] = $media_id;
                }
            }
        }

        // Обрабатываем видео (только сами видео файлы, без превью)
        if (!empty($media['videos'])) {
            $unique_video_urls = []; // Дополнительная защита от дублей видео

            foreach ($media['videos'] as $video) {
                // Загружаем только видео файл, если есть и он уникальный
                if (!empty($video['src']) &&
                    !in_array($video['src'], $processed_urls) &&
                    !in_array($video['src'], $unique_video_urls)) {

                    $processed_urls[] = $video['src'];
                    $unique_video_urls[] = $video['src'];

                    print_r('Uploading video: ' . $video['src'] . "\n");
                    $video_id = self::upload_media($video['src'], $post_id);
                    if ($video_id) {
                        $uploaded_media_ids[] = $video_id;
                    }
                }
                // Превью видео НЕ загружаем - они не нужны
            }
        }

        // Обновляем контент поста с загруженными медиа
        if (!empty($uploaded_media_ids)) {
            self::update_post_content_with_media($post_id, $uploaded_media_ids, $post_data['media']);
        }
    }

    private static function upload_media($media_url, $post_id) {
        // Check if this is a video file by URL extension
        $url_parts = parse_url($media_url);
        $path_info = pathinfo($url_parts['path'] ?? '');
        $extension = strtolower($path_info['extension'] ?? '');

        $video_extensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];

        if (in_array($extension, $video_extensions)) {
            print_r("Detected video file with extension: {$extension}. Checking file size...\n");

            // Check video file size before downloading
            $file_size = self::get_remote_file_size($media_url);
            $max_size = 20 * 1024 * 1024; // 20 MB in bytes

            if ($file_size === false) {
                print_r("Could not determine video file size. Skipping upload.\n");
                return false;
            }

            if ($file_size > $max_size) {
                $size_mb = round($file_size / (1024 * 1024), 2);
                print_r("Video file too large: {$size_mb} MB (max: 20 MB). Skipping upload.\n");
                return false;
            }

            $size_mb = round($file_size / (1024 * 1024), 2);
            print_r("Video file size: {$size_mb} MB. Proceeding with upload...\n");
        }
        // Получаем информацию о папке uploads
        $upload_dir = wp_upload_dir();

        // Создаем папку, если она не существует
        if (!file_exists($upload_dir['path'])) {
            wp_mkdir_p($upload_dir['path']);
            print_r('Created directory: ' . $upload_dir['path'] . "\n");
        }

        // Проверяем права на запись
        if (!wp_is_writable($upload_dir['path'])) {
            // Пытаемся установить права
            chmod($upload_dir['path'], 0775);
            if (!wp_is_writable($upload_dir['path'])) {
                error_log('TCP Error: Upload directory is not writable: ' . $upload_dir['path']);
                print_r('Upload directory is not writable: ' . $upload_dir['path'] . "\n");
                return false;
            }
        }

        require_once(ABSPATH . 'wp-admin/includes/image.php');
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/media.php');

        // Увеличиваем таймаут для загрузки медиа
        add_filter('http_request_timeout', function() {
            return 120; // 120 seconds for media downloads
        });

        print_r('Attempting to download media from: ' . $media_url . "\n");
        $media_id = media_sideload_image($media_url, $post_id, null, 'id');

        // Убираем фильтр после использования
        remove_all_filters('http_request_timeout');

        if (!is_wp_error($media_id)) {
            return $media_id;
        } else {
            print_r('Failed to upload image: ' . $media_id->get_error_message() . "\n");
            error_log('TCP Media Error: ' . $media_id->get_error_message());

            // Пробуем альтернативный метод загрузки
            print_r('Trying alternative upload method...' . "\n");
            return self::alternative_upload_media($media_url, $post_id);
        }
    }

    /**
     * Получает размер удаленного файла без его полной загрузки
     *
     * @param string $url URL файла
     * @return int|false Размер файла в байтах или false в случае ошибки
     */
    private static function get_remote_file_size($url) {
        $response = wp_remote_head($url, [
            'timeout' => 30,
            'redirection' => 5,
        ]);

        if (is_wp_error($response)) {
            print_r("Error checking file size: " . $response->get_error_message() . "\n");
            return false;
        }

        $headers = wp_remote_retrieve_headers($response);

        if (isset($headers['content-length'])) {
            return (int) $headers['content-length'];
        }

        // Если не удалось получить размер через HEAD запрос,
        // пробуем через GET с range запросом
        $response = wp_remote_get($url, [
            'timeout' => 30,
            'headers' => [
                'Range' => 'bytes=0-1'
            ]
        ]);

        if (is_wp_error($response)) {
            return false;
        }

        $headers = wp_remote_retrieve_headers($response);

        if (isset($headers['content-range'])) {
            $range = $headers['content-range'];
            $size = explode('/', $range);
            if (isset($size[1])) {
                return (int) $size[1];
            }
        }

        return false;
    }

    /**
     * Форматирует текст для правильного отображения в WordPress
     * Преобразует переносы строк в HTML-теги
     *
     * @param string $text Исходный текст
     * @return string Форматированный текст
     */
    private static function format_text_for_wordpress($text) {
        if (empty($text)) {
            return $text;
        }

        // Преобразуем переносы строк в HTML
        $formatted = nl2br($text);

        // Дополнительно обрабатываем двойные переносы как параграфы
        $formatted = wpautop($formatted);

        return $formatted;
    }

    private static function alternative_upload_media($media_url, $post_id) {
        $upload_dir = wp_upload_dir();

        // Получаем содержимое изображения
        $image_data = wp_remote_get($media_url, [
            'timeout' => 120, // Increase timeout to 120 seconds for large files
        ]);
        if (is_wp_error($image_data)) {
            print_r('Failed to download image: ' . $image_data->get_error_message() . "\n");
            return false;
        }

        $image_content = wp_remote_retrieve_body($image_data);
        if (empty($image_content)) {
            print_r('Empty image content' . "\n");
            return false;
        }

        // Определяем расширение файла
        $extension = '';
        $mime_type = '';

        // Пытаемся определить тип файла по содержимому
        $image_info = @getimagesizefromstring($image_content);
        if ($image_info !== false) {
            // Это изображение
            $mime_type = $image_info['mime'];
            switch ($mime_type) {
                case 'image/jpeg':
                    $extension = '.jpg';
                    break;
                case 'image/png':
                    $extension = '.png';
                    break;
                case 'image/gif':
                    $extension = '.gif';
                    break;
                case 'image/webp':
                    $extension = '.webp';
                    break;
                default:
                    $extension = '.jpg';
                    $mime_type = 'image/jpeg';
            }
        } else {
            // Пытаемся определить по URL или заголовкам
            $url_extension = strtolower(pathinfo(parse_url($media_url, PHP_URL_PATH), PATHINFO_EXTENSION));
            switch ($url_extension) {
                case 'mp4':
                    $extension = '.mp4';
                    $mime_type = 'video/mp4';
                    break;
                case 'webm':
                    $extension = '.webm';
                    $mime_type = 'video/webm';
                    break;
                case 'mov':
                    $extension = '.mov';
                    $mime_type = 'video/quicktime';
                    break;
                default:
                    // По умолчанию считаем изображением
                    $extension = '.jpg';
                    $mime_type = 'image/jpeg';
            }
        }

        // Создаем уникальное имя файла с дополнительной случайностью
        $filename = 'telegram_' . time() . '_' . $post_id . '_' . wp_rand(1000, 9999) . $extension;
        $file_path = $upload_dir['path'] . '/' . $filename;

        // Проверяем, что файл с таким именем не существует
        $counter = 1;
        while (file_exists($file_path)) {
            $filename = 'telegram_' . time() . '_' . $post_id . '_' . wp_rand(1000, 9999) . '_' . $counter . $extension;
            $file_path = $upload_dir['path'] . '/' . $filename;
            $counter++;
        }

        // Сохраняем файл
        $saved = file_put_contents($file_path, $image_content);
        if ($saved === false) {
            print_r('Failed to save image file: ' . $file_path . "\n");
            return false;
        }

        // Создаем запись в медиабиблиотеке
        $attachment = array(
            'guid' => $upload_dir['url'] . '/' . $filename,
            'post_mime_type' => $mime_type,
            'post_title' => sanitize_file_name($filename),
            'post_content' => '',
            'post_status' => 'inherit'
        );

        $attach_id = wp_insert_attachment($attachment, $file_path, $post_id);
        if (is_wp_error($attach_id)) {
            print_r('Failed to create attachment: ' . $attach_id->get_error_message() . "\n");
            return false;
        }

        // Генерируем метаданные
        require_once(ABSPATH . 'wp-admin/includes/image.php');

        if (strpos($mime_type, 'image/') === 0) {
            // Для изображений генерируем стандартные метаданные
            $attach_data = wp_generate_attachment_metadata($attach_id, $file_path);
            wp_update_attachment_metadata($attach_id, $attach_data);
            print_r('Image uploaded via alternative method (ID: ' . $attach_id . ')' . "\n");
        } else {
            // Для видео создаем базовые метаданные
            $attach_data = array(
                'file' => $filename,
                'filesize' => filesize($file_path)
            );
            wp_update_attachment_metadata($attach_id, $attach_data);
            print_r('Video uploaded via alternative method (ID: ' . $attach_id . ')' . "\n");
        }
        return $attach_id;
    }

    /**
     * Обновляет контент поста, встраивая загруженные медиа файлы
     */
    private static function update_post_content_with_media($post_id, $media_ids, $original_media_data = null) {
        if (empty($media_ids)) {
            return;
        }

        $post = get_post($post_id);
        if (!$post) {
            return;
        }

        $content = $post->post_content;
        $media_html = '';
        $images = [];
        $videos = [];

        // Группируем медиа по типам
        foreach ($media_ids as $media_id) {
            $attachment = get_post($media_id);
            if (!$attachment) {
                continue;
            }

            $mime_type = get_post_mime_type($media_id);

            if (strpos($mime_type, 'image/') === 0) {
                $images[] = $media_id;
            } elseif (strpos($mime_type, 'video/') === 0) {
                $videos[] = $media_id;
            }
        }

        // Добавляем изображения
        foreach ($images as $image_id) {
            $image_html = wp_get_attachment_image($image_id, 'large', false, [
                'style' => 'max-width: 100%; height: auto;'
            ]);
            $media_html .= $image_html . "\n";
        }

        // Добавляем видео с превью из исходных данных
        foreach ($videos as $video_id) {
            $video_url = wp_get_attachment_url($video_id);
            $mime_type = get_post_mime_type($video_id);

            // Ищем соответствующее превью в исходных данных
            $poster_attr = '';
            if ($original_media_data && !empty($original_media_data['videos'])) {
                foreach ($original_media_data['videos'] as $original_video) {
                    if (!empty($original_video['src']) && strpos($video_url, basename(parse_url($original_video['src'], PHP_URL_PATH))) !== false) {
                        if (!empty($original_video['thumb'])) {
                            $poster_attr = ' poster="' . esc_url($original_video['thumb']) . '"';
                        }
                        break;
                    }
                }
            }

            $media_html .= '<video controls style="max-width: 100%; height: auto;"' . $poster_attr . '>' . "\n";
            $media_html .= '  <source src="' . esc_url($video_url) . '" type="' . esc_attr($mime_type) . '">' . "\n";
            $media_html .= '  Ваш браузер не поддерживает видео.' . "\n";
            $media_html .= '</video>' . "\n";
        }

        if (!empty($media_html)) {
            // Добавляем медиа в конец контента
            $updated_content = $content . "\n\n" . $media_html;

            wp_update_post([
                'ID' => $post_id,
                'post_content' => $updated_content
            ]);

            print_r("Updated post content with " . count($images) . " images and " . count($videos) . " videos\n");
        }
    }
}