<?php
// Подключаем класс для очистки контента
require_once(plugin_dir_path(__FILE__) . 'class-content-modifier.php');


class TCP_Database {
    public static function activate() {
        // При активации плагина
    }

    public static function deactivate() {
        // При деактивации
    }

    public static function save_post($post_data) {
        print_r("\n");
        print_r("Processing post: " . $post_data['id'] . "\n");

        // Проверяем, существует ли уже пост с таким message_id
        $existing_posts = get_posts([
            'meta_key' => 'tcp_message_id',
            'meta_value' => $post_data['id'],
            'post_type' => 'post',
            'post_status' => 'any',
            'numberposts' => 1
        ]);

        if (!empty($existing_posts)) {
            print_r("Post with ID " . $post_data['id'] . " already exists (WordPress ID: " . $existing_posts[0]->ID . ")\n");
            return $existing_posts[0]->ID;
        }


        
        // Очищаем текст от упоминаний (@username)
        $cleaned_text = TCP_Content_Modifier::remove_mentions($post_data['text']);
        $cleaned_title = TCP_Content_Modifier::clean_title($cleaned_text, 100);
        



        // Создаем новый пост только с очищенным текстом
        $post_id = wp_insert_post([
            'post_title' => !empty($cleaned_title) ? $cleaned_title : wp_trim_words($cleaned_text, 10, '...'),
            'post_content' => $cleaned_text, // Очищенный текст, медиа добавим позже
            'post_status' => 'publish',
            'post_type' => 'post',
        ]);

        if (is_wp_error($post_id) || !$post_id) {
            error_log('TCP Error: Failed to create post');
            return false;
        }

        // Сохраняем метаданные
        update_post_meta($post_id, 'tcp_message_id', $post_data['id']);
        update_post_meta($post_id, 'tcp_channel', $post_data['channel']);

        // Обрабатываем медиа файлы и обновляем контент
        self::process_media($post_data, $post_id);

        return $post_id;
    }

    private static function process_media($post_data, $post_id) {
        if (empty($post_data['media'])) {
            return;
        }

        $media = $post_data['media'];
        $uploaded_media_ids = [];
        $processed_urls = []; // Отслеживаем уже обработанные URL

        // Обрабатываем изображения
        if (!empty($media['images'])) {
            foreach ($media['images'] as $image_url) {
                if (in_array($image_url, $processed_urls)) {
                    continue; // Пропускаем дубли
                }
                $processed_urls[] = $image_url;

                print_r('Uploading image: ' . $image_url . "\n");
                $media_id = self::upload_media($image_url, $post_id);

                if ($media_id) {
                    $uploaded_media_ids[] = $media_id;
                }
            }
        }

        // Обрабатываем видео (только сами видео файлы, без превью)
        if (!empty($media['videos'])) {
            $unique_video_urls = []; // Дополнительная защита от дублей видео

            foreach ($media['videos'] as $video) {
                // Загружаем только видео файл, если есть и он уникальный
                if (!empty($video['src']) &&
                    !in_array($video['src'], $processed_urls) &&
                    !in_array($video['src'], $unique_video_urls)) {

                    $processed_urls[] = $video['src'];
                    $unique_video_urls[] = $video['src'];

                    print_r('Uploading video: ' . $video['src'] . "\n");
                    $video_id = self::upload_media($video['src'], $post_id);
                    if ($video_id) {
                        $uploaded_media_ids[] = $video_id;
                    }
                }
                // Превью видео НЕ загружаем - они не нужны
            }
        }

        // Обновляем контент поста с загруженными медиа
        if (!empty($uploaded_media_ids)) {
            self::update_post_content_with_media($post_id, $uploaded_media_ids, $post_data['media']);
        }
    }

    private static function upload_media($image_url, $post_id) {
        // Получаем информацию о папке uploads
        $upload_dir = wp_upload_dir();

        // Создаем папку, если она не существует
        if (!file_exists($upload_dir['path'])) {
            wp_mkdir_p($upload_dir['path']);
            print_r('Created directory: ' . $upload_dir['path'] . "\n");
        }

        // Проверяем права на запись
        if (!wp_is_writable($upload_dir['path'])) {
            // Пытаемся установить права
            chmod($upload_dir['path'], 0775);
            if (!wp_is_writable($upload_dir['path'])) {
                error_log('TCP Error: Upload directory is not writable: ' . $upload_dir['path']);
                print_r('Upload directory is not writable: ' . $upload_dir['path'] . "\n");
                return false;
            }
        }

        require_once(ABSPATH . 'wp-admin/includes/image.php');
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/media.php');

        print_r('Attempting to download image from: ' . $image_url . "\n");
        $media_id = media_sideload_image($image_url, $post_id, null, 'id');

        if (!is_wp_error($media_id)) {
            return $media_id;
        } else {
            print_r('Failed to upload image: ' . $media_id->get_error_message() . "\n");
            error_log('TCP Media Error: ' . $media_id->get_error_message());

            // Пробуем альтернативный метод загрузки
            print_r('Trying alternative upload method...' . "\n");
            return self::alternative_upload_media($image_url, $post_id);
        }
    }

    private static function alternative_upload_media($image_url, $post_id) {
        $upload_dir = wp_upload_dir();

        // Получаем содержимое изображения
        $image_data = wp_remote_get($image_url);
        if (is_wp_error($image_data)) {
            print_r('Failed to download image: ' . $image_data->get_error_message() . "\n");
            return false;
        }

        $image_content = wp_remote_retrieve_body($image_data);
        if (empty($image_content)) {
            print_r('Empty image content' . "\n");
            return false;
        }

        // Определяем расширение файла
        $extension = '';
        $mime_type = '';

        // Пытаемся определить тип файла по содержимому
        $image_info = @getimagesizefromstring($image_content);
        if ($image_info !== false) {
            // Это изображение
            $mime_type = $image_info['mime'];
            switch ($mime_type) {
                case 'image/jpeg':
                    $extension = '.jpg';
                    break;
                case 'image/png':
                    $extension = '.png';
                    break;
                case 'image/gif':
                    $extension = '.gif';
                    break;
                case 'image/webp':
                    $extension = '.webp';
                    break;
                default:
                    $extension = '.jpg';
                    $mime_type = 'image/jpeg';
            }
        } else {
            // Пытаемся определить по URL или заголовкам
            $url_extension = strtolower(pathinfo(parse_url($image_url, PHP_URL_PATH), PATHINFO_EXTENSION));
            switch ($url_extension) {
                case 'mp4':
                    $extension = '.mp4';
                    $mime_type = 'video/mp4';
                    break;
                case 'webm':
                    $extension = '.webm';
                    $mime_type = 'video/webm';
                    break;
                case 'mov':
                    $extension = '.mov';
                    $mime_type = 'video/quicktime';
                    break;
                default:
                    // По умолчанию считаем изображением
                    $extension = '.jpg';
                    $mime_type = 'image/jpeg';
            }
        }

        // Создаем уникальное имя файла с дополнительной случайностью
        $filename = 'telegram_' . time() . '_' . $post_id . '_' . wp_rand(1000, 9999) . $extension;
        $file_path = $upload_dir['path'] . '/' . $filename;

        // Проверяем, что файл с таким именем не существует
        $counter = 1;
        while (file_exists($file_path)) {
            $filename = 'telegram_' . time() . '_' . $post_id . '_' . wp_rand(1000, 9999) . '_' . $counter . $extension;
            $file_path = $upload_dir['path'] . '/' . $filename;
            $counter++;
        }

        // Сохраняем файл
        $saved = file_put_contents($file_path, $image_content);
        if ($saved === false) {
            print_r('Failed to save image file: ' . $file_path . "\n");
            return false;
        }

        // Создаем запись в медиабиблиотеке
        $attachment = array(
            'guid' => $upload_dir['url'] . '/' . $filename,
            'post_mime_type' => $mime_type,
            'post_title' => sanitize_file_name($filename),
            'post_content' => '',
            'post_status' => 'inherit'
        );

        $attach_id = wp_insert_attachment($attachment, $file_path, $post_id);
        if (is_wp_error($attach_id)) {
            print_r('Failed to create attachment: ' . $attach_id->get_error_message() . "\n");
            return false;
        }

        // Генерируем метаданные
        require_once(ABSPATH . 'wp-admin/includes/image.php');

        if (strpos($mime_type, 'image/') === 0) {
            // Для изображений генерируем стандартные метаданные
            $attach_data = wp_generate_attachment_metadata($attach_id, $file_path);
            wp_update_attachment_metadata($attach_id, $attach_data);
            print_r('Image uploaded via alternative method (ID: ' . $attach_id . ')' . "\n");
        } else {
            // Для видео создаем базовые метаданные
            $attach_data = array(
                'file' => $filename,
                'filesize' => filesize($file_path)
            );
            wp_update_attachment_metadata($attach_id, $attach_data);
            print_r('Video uploaded via alternative method (ID: ' . $attach_id . ')' . "\n");
        }
        return $attach_id;
    }

    /**
     * Обновляет контент поста, встраивая загруженные медиа файлы
     */
    private static function update_post_content_with_media($post_id, $media_ids, $original_media_data = null) {
        if (empty($media_ids)) {
            return;
        }

        $post = get_post($post_id);
        if (!$post) {
            return;
        }

        $content = $post->post_content;
        $media_html = '';
        $images = [];
        $videos = [];

        // Группируем медиа по типам
        foreach ($media_ids as $media_id) {
            $attachment = get_post($media_id);
            if (!$attachment) {
                continue;
            }

            $mime_type = get_post_mime_type($media_id);

            if (strpos($mime_type, 'image/') === 0) {
                $images[] = $media_id;
            } elseif (strpos($mime_type, 'video/') === 0) {
                $videos[] = $media_id;
            }
        }

        // Добавляем изображения
        foreach ($images as $image_id) {
            $image_html = wp_get_attachment_image($image_id, 'large', false, [
                'style' => 'max-width: 100%; height: auto;'
            ]);
            $media_html .= $image_html . "\n";
        }

        // Добавляем видео с превью из исходных данных
        foreach ($videos as $video_id) {
            $video_url = wp_get_attachment_url($video_id);
            $mime_type = get_post_mime_type($video_id);

            // Ищем соответствующее превью в исходных данных
            $poster_attr = '';
            if ($original_media_data && !empty($original_media_data['videos'])) {
                foreach ($original_media_data['videos'] as $original_video) {
                    if (!empty($original_video['src']) && strpos($video_url, basename(parse_url($original_video['src'], PHP_URL_PATH))) !== false) {
                        if (!empty($original_video['thumb'])) {
                            $poster_attr = ' poster="' . esc_url($original_video['thumb']) . '"';
                        }
                        break;
                    }
                }
            }

            $media_html .= '<video controls style="max-width: 100%; height: auto;"' . $poster_attr . '>' . "\n";
            $media_html .= '  <source src="' . esc_url($video_url) . '" type="' . esc_attr($mime_type) . '">' . "\n";
            $media_html .= '  Ваш браузер не поддерживает видео.' . "\n";
            $media_html .= '</video>' . "\n";
        }

        if (!empty($media_html)) {
            // Добавляем медиа в конец контента
            $updated_content = $content . "\n\n" . $media_html;

            wp_update_post([
                'ID' => $post_id,
                'post_content' => $updated_content
            ]);

            print_r("Updated post content with " . count($images) . " images and " . count($videos) . " videos\n");
        }
    }
}