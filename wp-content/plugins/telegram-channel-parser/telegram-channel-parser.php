<?php
/*
Plugin Name: Telegram Channel Parser
Description: Парсит публичные Telegram-каналы и сохраняет посты в WordPress без дубликатов
Version: 1.1
Author: Ваше имя
*/

if (!defined('ABSPATH')) exit;

define('TCP_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('TCP_PLUGIN_URL', plugin_dir_url(__FILE__));

require_once TCP_PLUGIN_DIR . 'includes/class-parser.php';
require_once TCP_PLUGIN_DIR . 'includes/class-content-modifier.php';
require_once TCP_PLUGIN_DIR . 'includes/class-database.php';
require_once TCP_PLUGIN_DIR . 'includes/class-admin.php';

register_activation_hook(__FILE__, ['TCP_Database', 'activate']);
register_deactivation_hook(__FILE__, ['TCP_Database', 'deactivate']);

add_action('plugins_loaded', function() {
    new TCP_Admin();
});

if (defined('WP_CLI') && WP_CLI) {
    require_once TCP_PLUGIN_DIR . 'includes/class-cli.php';
}